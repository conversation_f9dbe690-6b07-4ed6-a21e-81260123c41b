import { useState, useCallback, useMemo } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { VariableWithContext } from '@/types/variable';
import { VariableChangeTracker, groupChangesByContext } from '@/lib/utils/changeTracking';
import { api } from '@/lib/services/api';

export interface UseVariableTreeStateOptions {
  templateId: number;
  onSaveSuccess?: () => void;
  onSaveError?: (error: string) => void;
}

export interface UseVariableTreeStateReturn {
  // State management
  variableChanges: Map<string, any>;
  hasChanges: boolean;

  // Actions
  updateVariable: (variableName: string, newValue: any, originalVariable: VariableWithContext, nodeId?: number, nodeType?: 'category' | 'dataset') => void;
  resetVariable: (variableName: string) => void;
  resetAllChanges: () => void;
  saveChanges: () => Promise<void>;

  // Reset to inherited functionality
  resetToInherited: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => Promise<void>;

  // Status
  isSaving: boolean;
  saveError: string | null;
  isResetting: boolean;
  resetError: string | null;

  // Helpers
  getVariableValue: (variableName: string, originalValue: any) => any;
  isVariableChanged: (variableName: string) => boolean;
}

/**
 * Hook for managing Variable Tree state changes and saving
 */
export function useVariableTreeState({
  templateId,
  onSaveSuccess,
  onSaveError
}: UseVariableTreeStateOptions): UseVariableTreeStateReturn {
  const [variableChanges, setVariableChanges] = useState<Map<string, any>>(new Map());
  const [changeTracker] = useState(() => new VariableChangeTracker());
  const queryClient = useQueryClient();

  // React Query mutations for each save context
  const updateTemplateMutation = useMutation({
    mutationFn: ({ templateId, variableOverrides }: { templateId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateTemplateVariables(templateId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const updateCategoryMutation = useMutation({
    mutationFn: ({ categoryId, variableOverrides }: { categoryId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateCategoryVariables(categoryId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const updateDatasetMutation = useMutation({
    mutationFn: ({ datasetId, variableOverrides }: { datasetId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateDatasetVariables(datasetId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  // Reset mutations for removing variable overrides
  const resetTemplateMutation = useMutation({
    mutationFn: ({ templateId, variableName }: { templateId: number; variableName: string }) =>
      api.resetTemplateVariable(templateId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const resetCategoryMutation = useMutation({
    mutationFn: ({ categoryId, variableName }: { categoryId: number; variableName: string }) =>
      api.resetCategoryVariable(categoryId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const resetDatasetMutation = useMutation({
    mutationFn: ({ datasetId, variableName }: { datasetId: number; variableName: string }) =>
      api.resetDatasetVariable(datasetId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  // Compute overall saving state and error from mutations
  const isSaving = updateTemplateMutation.isPending || updateCategoryMutation.isPending || updateDatasetMutation.isPending;
  const saveError = updateTemplateMutation.error?.message || updateCategoryMutation.error?.message || updateDatasetMutation.error?.message || null;

  // Compute overall resetting state and error from reset mutations
  const isResetting = resetTemplateMutation.isPending || resetCategoryMutation.isPending || resetDatasetMutation.isPending;
  const resetError = resetTemplateMutation.error?.message || resetCategoryMutation.error?.message || resetDatasetMutation.error?.message || null;

  // Update a variable value
  const updateVariable = useCallback((
    variableName: string,
    newValue: any,
    originalVariable: VariableWithContext,
    nodeId?: number,
    nodeType?: 'category' | 'dataset'
  ) => {
    setVariableChanges(prev => {
      const newChanges = new Map(prev);
      newChanges.set(variableName, newValue);
      return newChanges;
    });

    // Track the change for saving
    changeTracker.addChange(
      variableName,
      originalVariable.value,
      newValue,
      originalVariable,
      nodeId,
      nodeType
    );

    // Clear any previous save error (mutations handle their own error state)
  }, [changeTracker]);

  // Reset a single variable
  const resetVariable = useCallback((variableName: string) => {
    setVariableChanges(prev => {
      const newChanges = new Map(prev);
      newChanges.delete(variableName);
      return newChanges;
    });

    changeTracker.removeChange(variableName);
  }, [changeTracker]);

  // Reset all changes
  const resetAllChanges = useCallback(() => {
    setVariableChanges(new Map());
    changeTracker.clearChanges();
    // Reset mutation states
    updateTemplateMutation.reset();
    updateCategoryMutation.reset();
    updateDatasetMutation.reset();
  }, [changeTracker, updateTemplateMutation, updateCategoryMutation, updateDatasetMutation]);

  // Get the current value for a variable (changed or original)
  const getVariableValue = useCallback((variableName: string, originalValue: any) => {
    return variableChanges.has(variableName) ? variableChanges.get(variableName) : originalValue;
  }, [variableChanges]);

  // Check if a variable has been changed
  const isVariableChanged = useCallback((variableName: string) => {
    return variableChanges.has(variableName);
  }, [variableChanges]);

  // Check if there are any changes
  const hasChanges = useMemo(() => {
    return variableChanges.size > 0;
  }, [variableChanges]);

  // Save all changes using react-query mutations
  const saveChanges = useCallback(async () => {
    if (!hasChanges) return;

    try {
      const changes = changeTracker.getChanges();

      // Group changes by save context
      const changesByContext = groupChangesByContext(changes);

      // Save each group using appropriate mutation
      const savePromises: Promise<any>[] = [];

      changesByContext.forEach((contextChanges, contextKey) => {
        let saveLevel: string;
        let targetId: string | undefined;

        if (contextKey === 'template') {
          saveLevel = 'template';
          targetId = undefined;
        } else {
          [saveLevel, targetId] = contextKey.split('-');
        }

        // Create variable overrides object
        const variableOverrides: { [key: string]: any } = {};
        for (const change of contextChanges) {
          variableOverrides[change.variableName] = change.newValue;
        }

        if (saveLevel === 'template') {
          console.log('🔄 Saving to TEMPLATE level:', { templateId, variableOverrides });
          savePromises.push(updateTemplateMutation.mutateAsync({ templateId, variableOverrides }));
        } else if (saveLevel === 'category' && targetId && targetId !== 'template') {
          console.log('🔄 Saving to CATEGORY level:', { categoryId: parseInt(targetId), variableOverrides });
          savePromises.push(updateCategoryMutation.mutateAsync({ categoryId: parseInt(targetId), variableOverrides }));
        } else if (saveLevel === 'dataset' && targetId && targetId !== 'template') {
          console.log('🔄 Saving to DATASET level:', { datasetId: parseInt(targetId), variableOverrides });
          savePromises.push(updateDatasetMutation.mutateAsync({ datasetId: parseInt(targetId), variableOverrides }));
        }
      });

      // Wait for all mutations to complete
      await Promise.all(savePromises);

      // Clear changes after successful save
      resetAllChanges();
      onSaveSuccess?.();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save changes';
      onSaveError?.(errorMessage);
    }
  }, [hasChanges, changeTracker, templateId, resetAllChanges, onSaveSuccess, onSaveError, updateTemplateMutation, updateCategoryMutation, updateDatasetMutation]);

  // Reset to inherited functionality - removes variable override
  const resetToInherited = useCallback(async (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => {
    try {
      if (!nodeId || !nodeType) {
        // Template level reset
        await resetTemplateMutation.mutateAsync({ templateId, variableName });
      } else if (nodeType === 'category') {
        // Category level reset
        await resetCategoryMutation.mutateAsync({ categoryId: nodeId, variableName });
      } else if (nodeType === 'dataset') {
        // Dataset level reset
        await resetDatasetMutation.mutateAsync({ datasetId: nodeId, variableName });
      }

      // Also remove any local changes for this variable
      resetVariable(variableName);

    } catch (error) {
      console.error('Failed to reset variable to inherited:', error);
      throw error;
    }
  }, [templateId, resetTemplateMutation, resetCategoryMutation, resetDatasetMutation, resetVariable]);

  return {
    variableChanges,
    hasChanges,
    updateVariable,
    resetVariable,
    resetAllChanges,
    saveChanges,
    resetToInherited,
    isSaving,
    saveError,
    isResetting,
    resetError,
    getVariableValue,
    isVariableChanged
  };
}




